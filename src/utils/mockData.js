export const mockContacts = [
  {
    id: 'alex.m',
    name: '<PERSON>',
    secureId: '@alex.m',
    avatar: '👨‍💼',
    lastSeen: 'Online',
    status: 'Online'
  },
  {
    id: 'sophia.c',
    name: '<PERSON>',
    secureId: '@sophia.c',
    avatar: '👩‍💻',
    lastSeen: '2 min ago'
  },
  {
    id: 'ryan.h',
    name: '<PERSON>',
    secureId: '@ryan.h',
    avatar: '👨‍🎨',
    lastSeen: '5 min ago'
  },
  {
    id: 'olivia.w',
    name: '<PERSON>',
    secureId: '@olivia.w',
    avatar: '👩‍🔬',
    lastSeen: '1 hour ago'
  },
  {
    id: 'ethan.r',
    name: '<PERSON>',
    secureId: '@ethan.r',
    avatar: '👨‍🏫',
    lastSeen: 'Yesterday'
  },
  {
    id: 'liam.h',
    name: '<PERSON>',
    secureId: '@liam.h',
    avatar: '👨‍💼',
    status: 'Online'
  }
];

export const mockMessages = {
  'liam.h': [
    {
      id: 1,
      text: "Hey, how's it going?",
      sender: 'contact',
      timestamp: '10:00 AM',
      delivered: true
    },
    {
      id: 2,
      text: "Not bad, just finished a workout. You?",
      sender: 'user',
      timestamp: '10:01 AM',
      delivered: true
    },
    {
      id: 3,
      text: "Chilling at home, watching a movie. Any interesting happen today?",
      sender: 'contact',
      timestamp: '10:02 AM',
      delivered: true
    },
    {
      id: 4,
      text: "Nah, just the usual. Work, gym, repeat. How's the movie?",
      sender: 'user',
      timestamp: '10:03 AM',
      delivered: true
    }
  ]
};

export const mockGroupContacts = [
  { id: 'noah', name: 'Noah', status: 'Online', avatar: '👨‍💻' },
  { id: 'oliver', name: 'Oliver', lastSeen: '2 hours ago', avatar: '👨‍🎨' },
  { id: 'elijah', name: 'Elijah', status: 'Typing...', avatar: '👨‍🔬' },
  { id: 'james', name: 'James', status: 'Online', avatar: '👨‍🏫' },
  { id: 'william', name: 'William', lastSeen: 'yesterday', avatar: '👨‍⚕️' },
  { id: 'henry', name: 'Henry', status: 'Online', avatar: '👨‍🎓' },
  { id: 'lucas', name: 'Lucas', status: 'Online', avatar: '👨‍💼' },
  { id: 'theodore', name: 'Theodore', lastSeen: '10 minutes ago', avatar: '👨‍🔧' }
];