import React from 'react';
import './Avatar.css';

const Avatar = ({ 
  src, 
  alt, 
  emoji, 
  size = 'medium', 
  status, 
  className = '',
  ...props 
}) => {
  const avatarClass = `avatar avatar--${size} ${className}`.trim();

  return (
    <div className={avatarClass} {...props}>
      {src ? (
        <img src={src} alt={alt} className="avatar__image" />
      ) : emoji ? (
        <span className="avatar__emoji">{emoji}</span>
      ) : (
        <span className="avatar__placeholder">👤</span>
      )}
      {status && <div className={`avatar__status avatar__status--${status}`} />}
    </div>
  );
};

export default Avatar;