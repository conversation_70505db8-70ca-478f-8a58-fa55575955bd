import React from 'react';
import './Icon.css';

const Icon = ({ 
  name, 
  size = 'medium', 
  color, 
  className = '', 
  onClick,
  ...props 
}) => {
  const iconClass = `icon icon--${size} ${onClick ? 'icon--clickable' : ''} ${className}`.trim();
  
  const iconMap = {
    back: '←',
    add: '+',
    search: '🔍',
    user: '👤',
    lock: '🔒',
    phone: '📞',
    camera: '📷',
    attach: '📎',
    send: '➤',
    menu: '⋮',
    settings: '⚙️',
    chat: '💬',
    contacts: '👥',
    check: '✓',
    circle: '○',
    info: 'ⓘ',
    shield: '🛡️',
    bell: '🔔',
    help: '❓',
    feedback: '📝',
    privacy: '🔐'
  };

  const iconContent = iconMap[name] || name;

  return (
    <span 
      className={iconClass}
      style={{ color }}
      onClick={onClick}
      {...props}
    >
      {iconContent}
    </span>
  );
};

export default Icon;