.btn {
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Variants */
.btn--primary {
  background: #007AFF;
  color: white;
}

.btn--primary:hover:not(:disabled) {
  background: #0056CC;
}

.btn--secondary {
  background: none;
  color: #007AFF;
  border: 1px solid #007AFF;
}

.btn--secondary:hover:not(:disabled) {
  background: #007AFF;
  color: white;
}

.btn--ghost {
  background: none;
  color: #007AFF;
}

.btn--ghost:hover:not(:disabled) {
  background: #f0f0f0;
}

.btn--danger {
  background: #FF3B30;
  color: white;
}

.btn--danger:hover:not(:disabled) {
  background: #D70015;
}

.btn--icon {
  background: none;
  color: #8E8E93;
  padding: 8px;
  border-radius: 8px;
}

.btn--icon:hover:not(:disabled) {
  background: #f0f0f0;
}

/* Sizes */
.btn--small {
  padding: 8px 12px;
  font-size: 14px;
}

.btn--medium {
  padding: 12px 16px;
  font-size: 16px;
}

.btn--large {
  padding: 16px 24px;
  font-size: 18px;
}

.btn--full {
  width: 100%;
  padding: 16px;
  font-size: 16px;
}