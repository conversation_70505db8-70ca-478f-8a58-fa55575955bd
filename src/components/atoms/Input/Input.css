.input-container {
  position: relative;
  width: 100%;
}

.input {
  width: 100%;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  background: #f8f9fa;
  outline: none;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.input--with-icon {
  padding-left: 48px;
}

.input:focus {
  border-color: #007AFF;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.input--error {
  border-color: #FF3B30;
}

.input--error:focus {
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1);
}

.input:disabled {
  background: #f0f0f0;
  cursor: not-allowed;
  opacity: 0.6;
}

.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #8E8E93;
  z-index: 1;
  pointer-events: none;
}

.input-error {
  position: absolute;
  bottom: -20px;
  left: 0;
  font-size: 12px;
  color: #FF3B30;
}