import React from 'react';
import './Input.css';

const Input = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  icon,
  error,
  disabled = false,
  required = false,
  className = '',
  ...props
}) => {
  const inputClass = `input ${icon ? 'input--with-icon' : ''} ${error ? 'input--error' : ''} ${className}`.trim();

  return (
    <div className="input-container">
      {icon && <span className="input-icon">{icon}</span>}
      <input
        type={type}
        className={inputClass}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        required={required}
        {...props}
      />
      {error && <span className="input-error">{error}</span>}
    </div>
  );
};

export default Input;