import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import Icon from '../../atoms/Icon';
import './BottomNavigation.css';

const BottomNavigation = ({ className = '' }) => {
  const location = useLocation();

  const navItems = [
    { path: '/contacts', icon: 'chat', label: 'Chats' },
    { path: '/calls', icon: 'phone', label: 'Calls' },
    { path: '/stories', icon: 'camera', label: 'Stories' },
    { path: '/settings', icon: 'settings', label: 'Settings' }
  ];

  return (
    <nav className={`bottom-nav ${className}`.trim()}>
      {navItems.map(({ path, icon, label }) => {
        const isActive = location.pathname === path || 
          (path === '/contacts' && (location.pathname.startsWith('/chat') || location.pathname === '/contacts'));
        
        return (
          <Link
            key={path}
            to={path}
            className={`bottom-nav__item ${isActive ? 'bottom-nav__item--active' : ''}`}
          >
            <Icon name={icon} className="bottom-nav__icon" />
            <span className="bottom-nav__label">{label}</span>
          </Link>
        );
      })}
    </nav>
  );
};

export default BottomNavigation;