import React, { useEffect, useRef } from 'react';
import MessageBubble from '../../molecules/MessageBubble';
import './MessagesList.css';

const MessagesList = ({ 
  messages = [], 
  contact,
  className = '',
  ...props 
}) => {
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className={`messages-list ${className}`.trim()} {...props}>
      <div className="messages-list__container">
        {messages.length === 0 ? (
          <div className="messages-list__empty">
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message) => (
            <MessageBubble
              key={message.id}
              message={message}
              contact={contact}
            />
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default MessagesList;