import React from 'react';
import ContactItem from '../../molecules/ContactItem';
import './ContactsList.css';

const ContactsList = ({ 
  contacts = [], 
  onContactClick, 
  onContactMenu,
  title = 'All Contacts',
  emptyMessage = 'No contacts found',
  className = '',
  ...props 
}) => {
  return (
    <div className={`contacts-list ${className}`.trim()} {...props}>
      {title && <h3 className="contacts-list__title">{title}</h3>}
      
      {contacts.length === 0 ? (
        <div className="contacts-list__empty">
          <p>{emptyMessage}</p>
        </div>
      ) : (
        <div className="contacts-list__items">
          {contacts.map((contact) => (
            <ContactItem
              key={contact.id}
              contact={contact}
              onClick={onContactClick}
              onMenuClick={onContactMenu}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ContactsList;