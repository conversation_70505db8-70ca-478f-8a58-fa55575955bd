import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ScreenLayout from '../../templates/ScreenLayout';
import Header from '../../molecules/Header';
import MessagesList from '../../organisms/MessagesList';
import MessageInput from '../../molecules/MessageInput';
import Avatar from '../../atoms/Avatar';
import { mockContacts, mockMessages } from '../../../utils/mockData';

const ChatPage = ({ user }) => {
  const { contactId } = useParams();
  const navigate = useNavigate();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);

  const contact = mockContacts.find(c => c.id === contactId) || { 
    name: 'Unknown Contact', 
    avatar: '👤' 
  };

  useEffect(() => {
    const contactMessages = mockMessages[contactId] || [];
    setMessages(contactMessages);
  }, [contactId]);

  const handleSendMessage = (messageText) => {
    const newMessage = {
      id: messages.length + 1,
      text: messageText,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      delivered: false
    };
    
    setMessages(prev => [...prev, newMessage]);
    setMessage('');
    
    setTimeout(() => {
      setMessages(prev => prev.map(msg => 
        msg.id === newMessage.id ? { ...msg, delivered: true } : msg
      ));
    }, 1000);
  };

  const handleBack = () => {
    navigate('/contacts');
  };

  const handleCall = () => {
    alert('Voice call feature would be implemented here');
  };

  const handleAttach = () => {
    alert('Attach file feature');
  };

  const handleCamera = () => {
    alert('Camera feature');
  };

  const headerContent = (
    <div style={{ display: 'flex', alignItems: 'center', flex: 1, marginLeft: '16px' }}>
      <Avatar emoji={contact.avatar} size="small" />
      <div style={{ marginLeft: '12px' }}>
        <div style={{ fontSize: '18px', fontWeight: '600', color: '#000' }}>
          {contact.name}
        </div>
      </div>
    </div>
  );

  return (
    <ScreenLayout>
      <Header
        onBack={handleBack}
        onAction={handleCall}
        actionIcon="phone"
        actionLabel="Call"
      >
        {headerContent}
      </Header>

      <MessagesList 
        messages={messages}
        contact={contact}
      />

      <MessageInput
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onSubmit={handleSendMessage}
        onAttach={handleAttach}
        onCamera={handleCamera}
        placeholder="Type a message..."
      />
    </ScreenLayout>
  );
};

export default ChatPage;