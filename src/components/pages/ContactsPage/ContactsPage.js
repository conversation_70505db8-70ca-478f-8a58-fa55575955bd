import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ScreenLayout from '../../templates/ScreenLayout';
import Header from '../../molecules/Header';
import SearchBar from '../../molecules/SearchBar';
import ContactsList from '../../organisms/ContactsList';
import BottomNavigation from '../../organisms/BottomNavigation';
import { mockContacts } from '../../../utils/mockData';

const ContactsPage = ({ user, onLogout }) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');

  const filteredContacts = mockContacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.secureId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleContactClick = (contact) => {
    navigate(`/chat/${contact.id}`);
  };

  const handleContactMenu = (contact) => {
    alert(`Menu for ${contact.name}`);
  };

  const handleAddContact = () => {
    navigate('/group/new');
  };

  const handleBack = () => {
    onLogout();
  };

  return (
    <ScreenLayout>
      <Header
        title="Contacts"
        onBack={handleBack}
        onAction={handleAddContact}
        actionIcon="add"
        actionLabel="Add contact"
      />

      <div style={{ flex: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
        <SearchBar
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search contacts"
        />

        <div style={{ flex: 1, overflow: 'auto' }}>
          <ContactsList
            contacts={filteredContacts}
            onContactClick={handleContactClick}
            onContactMenu={handleContactMenu}
            title="All Contacts"
          />
        </div>
      </div>

      <BottomNavigation />
    </ScreenLayout>
  );
};

export default ContactsPage;