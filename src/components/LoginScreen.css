/* Login Screen with Blue Gradient Background */
.login-screen {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 25%, #1976D2 75%, #0D47A1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
  perspective: 1000px;
  overflow: hidden;
}

/* 3D Bouncing Globe Container */
.login-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 40px 32px;
  width: 400px;
  height: 400px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 0 50px rgba(255, 255, 255, 0.1),
    0 0 100px rgba(255, 255, 255, 0.2);
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  transform-style: preserve-3d;
  animation: bounce3D 8s infinite ease-in-out;
}

/* 3D Bouncing Animation */
@keyframes bounce3D {
  0% {
    transform: translate3d(0, 0, 0) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }
  12.5% {
    transform: translate3d(300px, -200px, 100px) rotateX(45deg) rotateY(90deg) rotateZ(15deg);
  }
  25% {
    transform: translate3d(200px, 150px, -50px) rotateX(-30deg) rotateY(180deg) rotateZ(-30deg);
  }
  37.5% {
    transform: translate3d(-250px, -100px, 80px) rotateX(60deg) rotateY(270deg) rotateZ(45deg);
  }
  50% {
    transform: translate3d(-150px, 200px, -100px) rotateX(-45deg) rotateY(360deg) rotateZ(-60deg);
  }
  62.5% {
    transform: translate3d(100px, -250px, 120px) rotateX(75deg) rotateY(450deg) rotateZ(30deg);
  }
  75% {
    transform: translate3d(250px, 100px, -80px) rotateX(-60deg) rotateY(540deg) rotateZ(-45deg);
  }
  87.5% {
    transform: translate3d(-200px, -150px, 60px) rotateX(30deg) rotateY(630deg) rotateZ(60deg);
  }
  100% {
    transform: translate3d(0, 0, 0) rotateX(0deg) rotateY(720deg) rotateZ(0deg);
  }
}

/* Globe surface effect */
.login-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: conic-gradient(
    from 0deg,
    rgba(255, 255, 255, 0.1) 0deg,
    rgba(255, 255, 255, 0.3) 90deg,
    rgba(255, 255, 255, 0.1) 180deg,
    rgba(255, 255, 255, 0.05) 270deg,
    rgba(255, 255, 255, 0.1) 360deg
  );
  border-radius: 50%;
  z-index: -1;
  animation: rotate 4s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Globe highlight effect */
.login-card::after {
  content: '';
  position: absolute;
  top: 20%;
  left: 30%;
  width: 40%;
  height: 40%;
  background: radial-gradient(
    ellipse at center,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  border-radius: 50%;
  z-index: 1;
  pointer-events: none;
}

/* User Icon Container */
.user-icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  z-index: 2;
  position: relative;
}

.user-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sign in Title */
.sign-in-title {
  color: white;
  font-size: 20px;
  font-weight: 400;
  margin: 0 0 20px 0;
  letter-spacing: 0.5px;
  z-index: 2;
  position: relative;
}

/* Form Styling */
.login-form {
  margin-bottom: 16px;
  z-index: 2;
  position: relative;
  width: 100%;
  max-width: 280px;
}

.form-group {
  margin-bottom: 12px;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  color: rgba(255, 255, 255, 0.7);
  z-index: 3;
}

.form-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  outline: none;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
}

.form-input:focus {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

/* Form Options (Remember me & Forgot password) */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 11px;
  z-index: 2;
  position: relative;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
}

.checkbox-container input[type="checkbox"] {
  margin-right: 6px;
  accent-color: rgba(255, 255, 255, 0.8);
  transform: scale(0.8);
}

.forgot-link {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 11px;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.forgot-link:hover {
  color: white;
  text-decoration: underline;
}

/* Login Button */
.login-button {
  width: 100%;
  max-width: 280px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;
  z-index: 2;
  position: relative;
}

.login-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

/* Signup Section */
.signup-section {
  text-align: center;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  z-index: 2;
  position: relative;
}

.signup-link {
  background: none;
  border: none;
  color: white;
  font-size: 11px;
  cursor: pointer;
  text-decoration: underline;
  font-weight: 500;
  transition: color 0.2s ease;
}

.signup-link:hover {
  color: rgba(255, 255, 255, 0.8);
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-screen {
    padding: 10px;
  }

  .login-card {
    width: 350px;
    height: 350px;
    padding: 30px 20px;
  }

  .user-icon {
    width: 40px;
    height: 40px;
  }

  .sign-in-title {
    font-size: 18px;
    margin-bottom: 16px;
  }

  .login-form {
    max-width: 250px;
  }

  .form-input {
    padding: 8px 10px 8px 32px;
    font-size: 13px;
  }

  .login-button {
    max-width: 250px;
    padding: 8px;
    font-size: 13px;
  }

  /* Adjust bounce animation for smaller screens */
  @keyframes bounce3D {
    0% {
      transform: translate3d(0, 0, 0) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
    }
    12.5% {
      transform: translate3d(150px, -100px, 50px) rotateX(45deg) rotateY(90deg) rotateZ(15deg);
    }
    25% {
      transform: translate3d(100px, 75px, -25px) rotateX(-30deg) rotateY(180deg) rotateZ(-30deg);
    }
    37.5% {
      transform: translate3d(-125px, -50px, 40px) rotateX(60deg) rotateY(270deg) rotateZ(45deg);
    }
    50% {
      transform: translate3d(-75px, 100px, -50px) rotateX(-45deg) rotateY(360deg) rotateZ(-60deg);
    }
    62.5% {
      transform: translate3d(50px, -125px, 60px) rotateX(75deg) rotateY(450deg) rotateZ(30deg);
    }
    75% {
      transform: translate3d(125px, 50px, -40px) rotateX(-60deg) rotateY(540deg) rotateZ(-45deg);
    }
    87.5% {
      transform: translate3d(-100px, -75px, 30px) rotateX(30deg) rotateY(630deg) rotateZ(60deg);
    }
    100% {
      transform: translate3d(0, 0, 0) rotateX(0deg) rotateY(720deg) rotateZ(0deg);
    }
  }
}

/* Add pause on hover for better usability */
.login-card:hover {
  animation-play-state: paused;
}

/* Add some floating particles effect */
.login-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.3), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.2), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 8s linear infinite;
  pointer-events: none;
}

@keyframes sparkle {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100px);
  }
}