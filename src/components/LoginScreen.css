/* Login Screen with Blue Gradient Background */
.login-screen {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 25%, #1976D2 75%, #0D47A1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

/* Glass-like Login Card */
.login-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 32px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* User Icon Container */
.user-icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.user-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sign in Title */
.sign-in-title {
  color: white;
  font-size: 24px;
  font-weight: 400;
  margin: 0 0 32px 0;
  letter-spacing: 0.5px;
}

/* Form Styling */
.login-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  color: rgba(255, 255, 255, 0.7);
  z-index: 2;
}

.form-input {
  width: 100%;
  padding: 14px 16px 14px 48px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  outline: none;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.form-input:focus {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

/* Form Options (Remember me & Forgot password) */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  font-size: 14px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
}

.checkbox-container input[type="checkbox"] {
  margin-right: 8px;
  accent-color: rgba(255, 255, 255, 0.8);
}

.forgot-link {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.forgot-link:hover {
  color: white;
  text-decoration: underline;
}

/* Login Button */
.login-button {
  width: 100%;
  padding: 14px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 24px;
}

.login-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

/* Signup Section */
.signup-section {
  text-align: center;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.signup-link {
  background: none;
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
  font-weight: 500;
  transition: color 0.2s ease;
}

.signup-link:hover {
  color: rgba(255, 255, 255, 0.8);
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-screen {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
  }

  .user-icon {
    width: 50px;
    height: 50px;
  }

  .sign-in-title {
    font-size: 20px;
    margin-bottom: 24px;
  }
}