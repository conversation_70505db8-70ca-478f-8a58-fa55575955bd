.message {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  margin-bottom: 16px;
}

.message--sent {
  flex-direction: row-reverse;
}

.message--received {
  flex-direction: row;
}

.message__avatar {
  flex-shrink: 0;
}

.message__avatar--user {
  background: #007AFF;
  color: white;
}

.message__content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.message--sent .message__content {
  align-items: flex-end;
}

.message--received .message__content {
  align-items: flex-start;
}

.message__bubble {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 16px;
  line-height: 1.4;
  word-wrap: break-word;
}

.message--sent .message__bubble {
  background: #007AFF;
  color: white;
  border-bottom-right-radius: 6px;
}

.message--received .message__bubble {
  background: white;
  color: #000;
  border: 1px solid #e0e0e0;
  border-bottom-left-radius: 6px;
}

.message__info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  font-size: 12px;
  color: #8E8E93;
}

.message__sender {
  font-weight: 500;
}

.message__time {
  color: #8E8E93;
}

.message__status {
  color: #007AFF;
}