import React from 'react';
import Avatar from '../../atoms/Avatar';
import Icon from '../../atoms/Icon';
import './MessageBubble.css';

const MessageBubble = ({ 
  message, 
  contact,
  className = '',
  ...props 
}) => {
  const { text, sender, timestamp, delivered } = message;
  const isSent = sender === 'user';

  return (
    <div 
      className={`message ${isSent ? 'message--sent' : 'message--received'} ${className}`.trim()}
      {...props}
    >
      {!isSent && (
        <Avatar 
          emoji={contact?.avatar} 
          size="small"
          className="message__avatar"
        />
      )}
      
      <div className="message__content">
        <div className="message__bubble">
          {text}
        </div>
        <div className="message__info">
          {!isSent && contact?.name && (
            <span className="message__sender">{contact.name}</span>
          )}
          <span className="message__time">{timestamp}</span>
          {isSent && (
            <span className="message__status">
              <Icon name={delivered ? 'check' : 'circle'} size="small" />
            </span>
          )}
        </div>
      </div>
      
      {isSent && (
        <Avatar 
          emoji="👤" 
          size="small"
          className="message__avatar message__avatar--user"
        />
      )}
    </div>
  );
};

export default MessageBubble;