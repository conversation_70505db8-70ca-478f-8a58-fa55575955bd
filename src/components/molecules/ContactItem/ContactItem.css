.contact-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-radius: 12px;
  transition: background-color 0.2s ease;
}

.contact-item--clickable {
  cursor: pointer;
}

.contact-item--clickable:hover {
  background-color: #f8f9fa;
}

.contact-item__info {
  flex: 1;
  margin-left: 16px;
}

.contact-item__name {
  font-size: 16px;
  font-weight: 500;
  color: #000;
  margin-bottom: 4px;
}

.contact-item__id {
  font-size: 14px;
  color: #8E8E93;
}

.contact-item__status {
  font-size: 14px;
  color: #8E8E93;
}

.contact-item__menu {
  margin-left: 8px;
}