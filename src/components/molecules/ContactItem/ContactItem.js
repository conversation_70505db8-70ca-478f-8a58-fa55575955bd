import React from 'react';
import Avatar from '../../atoms/Avatar';
import Button from '../../atoms/Button';
import Icon from '../../atoms/Icon';
import './ContactItem.css';

const ContactItem = ({ 
  contact, 
  onClick, 
  onMenuClick,
  showMenu = true,
  className = '',
  ...props 
}) => {
  const { name, secureId, avatar, lastSeen, status } = contact;

  const handleClick = () => {
    if (onClick) onClick(contact);
  };

  const handleMenuClick = (e) => {
    e.stopPropagation();
    if (onMenuClick) onMenuClick(contact);
  };

  return (
    <div 
      className={`contact-item ${onClick ? 'contact-item--clickable' : ''} ${className}`.trim()}
      onClick={handleClick}
      {...props}
    >
      <Avatar 
        emoji={avatar} 
        size="medium"
        status={status === 'Online' ? 'online' : status === 'Typing...' ? 'online' : undefined}
      />
      
      <div className="contact-item__info">
        <div className="contact-item__name">{name}</div>
        {secureId && (
          <div className="contact-item__id">SecureChat ID: {secureId}</div>
        )}
        {lastSeen && (
          <div className="contact-item__status">{lastSeen}</div>
        )}
        {status && !lastSeen && (
          <div className="contact-item__status">{status}</div>
        )}
      </div>
      
      {showMenu && onMenuClick && (
        <Button
          variant="ghost"
          size="small"
          onClick={handleMenuClick}
          className="contact-item__menu"
        >
          <Icon name="menu" />
        </Button>
      )}
    </div>
  );
};

export default ContactItem;