import React from 'react';
import Input from '../../atoms/Input';
import Icon from '../../atoms/Icon';
import './SearchBar.css';

const SearchBar = ({ 
  value, 
  onChange, 
  placeholder = 'Search...', 
  className = '',
  ...props 
}) => {
  return (
    <div className={`search-bar ${className}`.trim()}>
      <Input
        type="text"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        icon={<Icon name="search" size="small" />}
        {...props}
      />
    </div>
  );
};

export default SearchBar;