import React from 'react';
import Button from '../../atoms/Button';
import Icon from '../../atoms/Icon';
import './Header.css';

const Header = ({ 
  title, 
  onBack, 
  onAction, 
  actionIcon, 
  actionLabel,
  showBackButton = true,
  className = '',
  children 
}) => {
  return (
    <div className={`header ${className}`.trim()}>
      {showBackButton && onBack && (
        <Button
          variant="ghost"
          size="small"
          onClick={onBack}
          className="header__back"
        >
          <Icon name="back" />
        </Button>
      )}
      
      {children || (
        <div className="header__title">{title}</div>
      )}
      
      {onAction && (
        <Button
          variant="ghost"
          size="small"
          onClick={onAction}
          className="header__action"
          aria-label={actionLabel}
        >
          <Icon name={actionIcon} />
        </Button>
      )}
      
      {!onAction && <div className="header__spacer" />}
    </div>
  );
};

export default Header;