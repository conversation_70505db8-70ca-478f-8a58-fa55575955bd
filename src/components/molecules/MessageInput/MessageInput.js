import React from 'react';
import Button from '../../atoms/Button';
import Icon from '../../atoms/Icon';
import './MessageInput.css';

const MessageInput = ({ 
  value, 
  onChange, 
  onSubmit,
  onAttach,
  onCamera,
  placeholder = 'Type a message...',
  disabled = false,
  className = '',
  ...props 
}) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    if (value.trim() && onSubmit) {
      onSubmit(value.trim());
    }
  };

  const handleAttach = () => {
    if (onAttach) onAttach();
  };

  const handleCamera = () => {
    if (onCamera) onCamera();
  };

  return (
    <form 
      className={`message-input ${className}`.trim()}
      onSubmit={handleSubmit}
      {...props}
    >
      <div className="message-input__wrapper">
        <input
          type="text"
          className="message-input__field"
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          disabled={disabled}
        />
        
        <Button
          type="button"
          variant="ghost"
          size="small"
          onClick={handleAttach}
          className="message-input__button"
        >
          <Icon name="attach" />
        </Button>
        
        <Button
          type="button"
          variant="ghost"
          size="small"
          onClick={handleCamera}
          className="message-input__button"
        >
          <Icon name="camera" />
        </Button>
        
        <Button
          type="submit"
          variant="primary"
          size="small"
          disabled={!value.trim() || disabled}
          className="message-input__send"
        >
          <Icon name="send" color="white" />
        </Button>
      </div>
    </form>
  );
};

export default MessageInput;