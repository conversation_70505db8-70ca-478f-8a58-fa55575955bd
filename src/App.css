.app {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

/* Screen container for non-login screens */
.screen-container {
  width: 390px;
  height: 844px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 50px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Override for login screen to use full viewport */
.login-screen {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.back-button {
  background: none;
  border: none;
  font-size: 18px;
  color: #007AFF;
  cursor: pointer;
  margin-right: 16px;
  padding: 8px;
  border-radius: 8px;
}

.back-button:hover {
  background-color: #f0f0f0;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  flex: 1;
  text-align: center;
}

.header-action {
  background: none;
  border: none;
  font-size: 18px;
  color: #007AFF;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
}

.header-action:hover {
  background-color: #f0f0f0;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.bottom-nav {
  display: flex;
  background: white;
  border-top: 1px solid #f0f0f0;
  padding: 12px 0 24px;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #8E8E93;
  padding: 8px;
}

.nav-item.active {
  color: #007AFF;
}

.nav-item:hover {
  background-color: #f0f0f0;
  border-radius: 8px;
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-label {
  font-size: 12px;
  font-weight: 500;
}

.input-field {
  width: 100%;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  background: #f8f9fa;
  outline: none;
  transition: border-color 0.2s;
}

.input-field:focus {
  border-color: #007AFF;
  background: white;
}

.primary-button {
  width: 100%;
  padding: 16px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primary-button:hover {
  background: #0056CC;
}

.secondary-button {
  background: none;
  border: none;
  color: #007AFF;
  font-size: 16px;
  cursor: pointer;
  text-decoration: none;
  padding: 8px;
}

.secondary-button:hover {
  text-decoration: underline;
}