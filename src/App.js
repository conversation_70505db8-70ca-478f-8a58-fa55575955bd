import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import LoginScreen from './components/LoginScreen';
import { ContactsPage, ChatPage } from './components/pages';
import SettingsScreen from './components/SettingsScreen';
import DevicesScreen from './components/DevicesScreen';
import GroupScreen from './components/GroupScreen';
import './App.css';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  const handleLogin = (userData) => {
    setIsAuthenticated(true);
    setCurrentUser(userData);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setCurrentUser(null);
  };

  return (
    <Router>
      <div className="app">
        <Routes>
          <Route 
            path="/login" 
            element={
              !isAuthenticated ? 
                <LoginScreen onLogin={handleLogin} /> : 
                <Navigate to="/contacts" replace />
            } 
          />
          <Route 
            path="/contacts" 
            element={
              isAuthenticated ? 
                <ContactsPage user={currentUser} onLogout={handleLogout} /> : 
                <Navigate to="/login" replace />
            } 
          />
          <Route 
            path="/chat/:contactId" 
            element={
              isAuthenticated ? 
                <ChatPage user={currentUser} /> : 
                <Navigate to="/login" replace />
            } 
          />
          <Route 
            path="/settings" 
            element={
              isAuthenticated ? 
                <SettingsScreen user={currentUser} /> : 
                <Navigate to="/login" replace />
            } 
          />
          <Route 
            path="/devices" 
            element={
              isAuthenticated ? 
                <DevicesScreen user={currentUser} /> : 
                <Navigate to="/login" replace />
            } 
          />
          <Route 
            path="/group/new" 
            element={
              isAuthenticated ? 
                <GroupScreen user={currentUser} /> : 
                <Navigate to="/login" replace />
            } 
          />
          <Route path="/" element={<Navigate to="/login" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;